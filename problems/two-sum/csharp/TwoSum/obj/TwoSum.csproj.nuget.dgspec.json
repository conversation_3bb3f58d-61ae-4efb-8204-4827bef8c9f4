{"format": 1, "restore": {"/Users/<USER>/workspace/repositories/github/KerimAkici/coding-challenges/problems/two-sum/csharp/TwoSum/TwoSum.csproj": {}}, "projects": {"/Users/<USER>/workspace/repositories/github/KerimAkici/coding-challenges/problems/two-sum/csharp/TwoSum/TwoSum.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/workspace/repositories/github/Kerim<PERSON>kici/coding-challenges/problems/two-sum/csharp/TwoSum/TwoSum.csproj", "projectName": "TwoSum", "projectPath": "/Users/<USER>/workspace/repositories/github/Kerim<PERSON>kici/coding-challenges/problems/two-sum/csharp/TwoSum/TwoSum.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/workspace/repositories/github/Kerim<PERSON>ki<PERSON>/coding-challenges/problems/two-sum/csharp/TwoSum/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.14.1, )"}, "xunit": {"target": "Package", "version": "[2.9.3, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[3.1.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}