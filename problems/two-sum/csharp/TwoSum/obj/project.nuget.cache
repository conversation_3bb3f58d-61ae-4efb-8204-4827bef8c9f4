{"version": 2, "dgSpecHash": "iJazl8k0Ih4=", "success": true, "projectFilePath": "/Users/<USER>/workspace/repositories/github/Kerim<PERSON>kici/coding-challenges/problems/two-sum/csharp/TwoSum/TwoSum.csproj", "expectedPackageFiles": ["/Users/<USER>/.nuget/packages/microsoft.codecoverage/17.14.1/microsoft.codecoverage.17.14.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.net.test.sdk/17.14.1/microsoft.net.test.sdk.17.14.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.objectmodel/17.14.1/microsoft.testplatform.objectmodel.17.14.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/microsoft.testplatform.testhost/17.14.1/microsoft.testplatform.testhost.17.14.1.nupkg.sha512", "/Users/<USER>/.nuget/packages/newtonsoft.json/13.0.3/newtonsoft.json.13.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.collections.immutable/8.0.0/system.collections.immutable.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/system.reflection.metadata/8.0.0/system.reflection.metadata.8.0.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit/2.9.3/xunit.2.9.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.abstractions/2.0.3/xunit.abstractions.2.0.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.analyzers/1.18.0/xunit.analyzers.1.18.0.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.assert/2.9.3/xunit.assert.2.9.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.core/2.9.3/xunit.core.2.9.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.extensibility.core/2.9.3/xunit.extensibility.core.2.9.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.extensibility.execution/2.9.3/xunit.extensibility.execution.2.9.3.nupkg.sha512", "/Users/<USER>/.nuget/packages/xunit.runner.visualstudio/3.1.4/xunit.runner.visualstudio.3.1.4.nupkg.sha512"], "logs": []}